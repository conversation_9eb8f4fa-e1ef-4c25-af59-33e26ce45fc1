/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import type React from "react";
import { useState, useEffect, useCallback, useMemo } from "react";
import { useSelector } from "react-redux";
import type { RootState } from "@/store/store";
import MintStages from "./MintStages";
import MintInfo from "./MintInfo";
import MintButton from "./MintButton";
import { NftPreview } from "./NftPreview";
import { MintPanelSkeleton } from "./MintPanelSkeleton";
import { toast } from "sonner";
import { uploadFilePinata } from "@/lib/utils/upload";
import Image from "next/image";
import {
  ArtType,
  useMintNftMutation,
  type MintNftInput,
  SubmitMintTransactionInput,
  useSubmitMintTransactionMutation,
  Step,
  NftMetadataInput,
  AttributeInput,
  useGetNftsQuery,
  SortOrder,
  FilterOperator,
  useNftMintedRealtimeSubscription,
  useGetActiveStageQuery,
  useGetMintCostQuery,
  useNftModifiedPrivateRealtimeSubscription,
  ActionType,
} from "@/lib/api/graphql/generated";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Info, Sparkles, Loader2 } from "lucide-react";
import { useAccount, useWalletClient } from "wagmi";
import MintProgress from "@/components/features/mint/MintPanel/MintProgress";
import { v4 as uuidv4 } from "uuid";
import { ethers } from "ethers";
import { NFTManager } from "@/lib/abi/NFTManager";
import { getChainsConfig } from "@/lib/blockchain/walletConfig";
import { ScrollArea } from "@/components/ui/scroll-area";
interface MintPanelProps {
  currentGalleryImage?: string;
}

export default function MintPanel({ currentGalleryImage }: MintPanelProps) {
  const { isConnected, address, chain: chainObject } = useAccount();
  const collection = useSelector((state: RootState) => state.collection.data);
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [imageFile, setImageFile] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [attributes, setAttributes] = useState<AttributeInput[]>([]);
  const [amount, setAmount] = useState(1);
  const [royalty, setRoyalty] = useState(0);
  const [gasPrice, setGasPrice] = useState<string | undefined>(undefined);
  const [gasLimit, setGasLimit] = useState<string | undefined>(undefined);
  const [signature, setSignature] = useState<string | undefined>(undefined);
  const [nonce, setNonce] = useState<string | undefined>(undefined);
  const [nameError, setNameError] = useState<string | undefined>(undefined);
  const [descriptionError, setDescriptionError] = useState<string | undefined>(
    undefined
  );
  const [attributesError, setAttributesError] = useState<string | undefined>(
    undefined
  );
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isBatchMint, setIsBatchMint] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("mint");
  const [batchMetadata, setBatchMetadata] = useState<NftMetadataInput[]>([]);
  const [mintSteps, setMintSteps] = useState<Step[]>([]);
  const { data: walletClient } = useWalletClient();
  const [submitMintTransaction, { loading: isSubmitting }] =
    useSubmitMintTransactionMutation();
  const isSameArtType = collection?.artType === ArtType.Same;

  const { data: activeStageData, error: activeStageError } =
    useGetActiveStageQuery({
      variables: {
        input: {
          chainId: collection?.chainId ?? "",
          contractAddress: collection?.contractAddress ?? "",
          wallet: address ?? "",
        },
      },
      skip: !collection?.chainId || !collection?.contractAddress || !address,
    });

  const [mintNft, { loading: isMintingNft }] = useMintNftMutation();
  const isAllowlistMint =
    activeStageData?.getActiveStage?.isPublicMint === false;

  const inputQuery = {
    filters: [
      {
        field: "collectionId",
        operator: FilterOperator.Eq,
        value: collection?.id || "",
      },
      {
        field: "creator",
        operator: FilterOperator.Eq,
        value: address?.toLowerCase() || "",
      },
    ],
    pagination: {
      limit: "20",
      skip: "0",
    },
    sort: {
      field: "createdAt",
      order: SortOrder.Desc,
    },
  };

  const { data: nftsData, refetch: refetchNfts } = useGetNftsQuery({
    skip: !collection || !isConnected,
    variables: {
      input: inputQuery,
    },
  });

  // useNftModifiedPrivateRealtimeSubscription({
  //   variables: {
  //     input: {
  //       contractAddress: collection?.contractAddress ?? "",
  //       wallet: address ?? "",
  //     },
  //   },
  //   onData: ({ data, client }) => {
  //     const newData = data.data?.nftModifiedPrivateRealtime;
  //     switch (newData?.action) {
  //       case ActionType.Create:
  //         refetchNfts();
  //         break;
  //     }
  //   },
  // });
  useNftMintedRealtimeSubscription({
    variables: {
      input: {
        contractAddress: collection?.contractAddress ?? "",
        wallet: address ?? "",
      },
    },
    onData: ({ data }) => {
      toast.success("NFT minted successfully", {
        description: `Token IDs: ${data?.data?.nftMintedRealtime.tokenIds.join(
          ", "
        )}`,
      });
      refetchNfts();
    },
    onError: (error) => {
      toast.error("Failed to fetch nft minted realtime", {
        description: error.message,
      });
    },
    skip: !collection?.contractAddress || !isConnected || !address,
  });

  // Khởi tạo dữ liệu mặc định
  useMemo(() => {
    if (collection) {
      setName(`${collection.name}`);
      setDescription(collection.description || "");
      setPreviewUrl(
        collection.artType === ArtType.Same ? collection.image : null
      );
    }
  }, [collection]);

  const [currentAmount, setCurrentAmount] = useState(amount);
  const [isLoading, setIsLoading] = useState(false);
  const [lastMintCost, setLastMintCost] = useState({
    mintPrice: "0",
    estimatedGas: "0",
    totalPrice: "0",
  });

  const { data: mintCostData, error: mintCostError } = useGetMintCostQuery({
    variables: {
      input: {
        chainId: collection?.chainId ?? "",
        contractAddress: collection?.contractAddress ?? "",
        amount: currentAmount.toString(),
        wallet: address ?? "",
        stageId: activeStageData?.getActiveStage?.stageId,
      },
    },
    skip: !collection?.chainId || !collection?.contractAddress || isLoading,
    onError: (error) => {
      toast.error("Failed to fetch mint cost", {
        description: error.message,
      });
    },
  });

  // Update lastMintCost when mintCostData changes
  useEffect(() => {
    if (mintCostData?.getMintCost.success) {
      setLastMintCost({
        mintPrice: mintCostData.getMintCost.mintPrice,
        estimatedGas: mintCostData.getMintCost.estimatedGas,
        totalPrice: mintCostData.getMintCost.totalPrice,
      });
    }
  }, [mintCostData]);

  // Update currentAmount when amount or batchMetadata changes
  useEffect(() => {
    const newAmount = batchMetadata.length
      ? batchMetadata.reduce((sum, meta) => sum + Number(meta.amount), 0)
      : amount;

    if (newAmount !== currentAmount) {
      setIsLoading(true);
      setCurrentAmount(newAmount);
      // Add a small delay to prevent UI flickering
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [amount, batchMetadata]);

  // Handle mintCostError
  useEffect(() => {
    if (mintCostError) {
      toast.error("Failed to fetch mint cost", {
        description: mintCostError.message,
      });
    }
  }, [mintCostError]);

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;
    setIsUploading(true);
    try {
      if (!["image/png", "image/jpeg", "image/jpg"].includes(file.type)) {
        toast.error("Only PNG, JPEG, or JPG files are allowed", {
          action: {
            label: "Retry",
            onClick: () => document.getElementById("image-upload")?.click(),
          },
        });
        return;
      }

      const fileSizeMB = file.size / 1024 / 1024;
      if (fileSizeMB > 10) {
        toast.error("File size must be less than 10MB", {
          action: {
            label: "Retry",
            onClick: () => document.getElementById("image-upload")?.click(),
          },
        });
        return;
      }

      const reader = new FileReader();
      reader.onload = () => setPreviewUrl(reader.result as string);
      reader.readAsDataURL(file);
      const ipfsUrl = await uploadFilePinata(file);
      if (!/^(ipfs|https):\/\//.test(ipfsUrl)) {
        toast.error("Uploaded file must be an IPFS or HTTPS URL");
        return;
      }
      setImageFile(ipfsUrl);
      toast.success("Image uploaded to IPFS");
    } catch (error) {
      toast.error("Upload failed", { description: String(error) });
      setPreviewUrl(null);
      event.target.value = "";
    } finally {
      setIsUploading(false);
    }
  };

  const handleBatchUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;
    if (files.length > 10) {
      toast.error("Maximum 10 images allowed for batch upload");
      return;
    }

    setIsUploading(true);
    try {
      const newBatchMetadata = await Promise.all(
        Array.from(files).map(async (file, index) => {
          if (!["image/png", "image/jpeg", "image/jpg"].includes(file.type)) {
            throw new Error(`File ${file.name} must be PNG, JPEG, or JPG`);
          }
          const fileSizeMB = file.size / 1024 / 1024;
          if (fileSizeMB > 10) {
            throw new Error(`File ${file.name} must be less than 10MB`);
          }

          const ipfsUrl = await uploadFilePinata(file);
          if (!/^(ipfs|https):\/\//.test(ipfsUrl)) {
            throw new Error(`Invalid IPFS/HTTPS URL for ${file.name}`);
          }

          return {
            amount: "1",
            name: `${collection?.name || "NFT"} #${
              Number(collection?.totalMinted || 0) + index + 1
            }`,
            description: collection?.description || "",
            imageFile: ipfsUrl,
            attributes: [],
          };
        })
      );

      setBatchMetadata(newBatchMetadata);
      toast.success(
        `Uploaded ${newBatchMetadata.length} images for batch mint`
      );
    } catch (error) {
      toast.error("Batch upload failed", { description: String(error) });
    } finally {
      setIsUploading(false);
      event.target.value = "";
    }
  };

  const onMintSuccess = () => {
    setImageFile(null);
    setPreviewUrl(
      collection?.artType === ArtType.Same ? collection.image : null
    );
    setName("");
    setDescription("");
    setAttributes([]);
    setAmount(1);
    setRoyalty(0);
    setGasPrice(undefined);
    setGasLimit(undefined);
    setSignature(undefined);
    setNonce(undefined);
    setAgreedToTerms(false);
    setBatchMetadata([]);
    refetchNfts();
  };

  const handleMintConfirm = async (isBatch: boolean) => {
    try {
      if (!mintCostData?.getMintCost.success || !collection || !address) {
        throw new Error(
          mintCostData?.getMintCost.errorMessage || "Invalid mint cost data"
        );
      }

      setIsBatchMint(isBatch);
      let tokenUris: string[] = [];
      let totalAmount = amount;
      let batchAmounts: string[] | undefined;

      // Get next token ID from contract
      const provider = getChainsConfig()[collection.chainId].provider;
      console.log("Contract address:", collection.contractAddress);
      console.log("Chain ID:", collection.chainId);

      const contract = new ethers.Contract(
        collection.contractAddress,
        NFTManager.abi,
        provider
      );

      const nextTokenId = await contract.getNextTokenId();

      const generateMetadata = (
        index: number,
        baseName: string,
        baseDescription: string,
        baseImage: string,
        baseAttributes: any[]
      ) => ({
        name: `${baseName} #${Number(nextTokenId) + index}`,
        description: baseDescription,
        image: baseImage,
        attributes: baseAttributes,
      });

      if (isSameArtType) {
        // Single mint hoặc Batch mint cho SAME
        const baseName = name.trim() || collection.name;
        const baseDescription =
          description.trim() || collection.description || "";
        const baseImage =
          currentGalleryImage?.replace(
            /^https?:\/\/[^/]+\/ipfs\/(.+)$/,
            "ipfs://$1"
          ) ||
          collection.image.replace(
            /^https?:\/\/[^/]+\/ipfs\/(.+)$/,
            "ipfs://$1"
          );
        const baseAttributes = attributes.filter(
          (attr) => attr.trait_type.trim() && attr.value.trim()
        );

        if (isBatch) {
          // Batch mint cho SAME
          tokenUris = await Promise.all(
            batchMetadata.map(async (meta, index) => {
              const metadataJson = JSON.stringify(
                generateMetadata(
                  index,
                  baseName,
                  baseDescription,
                  baseImage,
                  baseAttributes
                )
              );
              const fileName = `${baseName}-${address}-metadata-${uuidv4()}.json`;
              const tokenUri = await uploadFilePinata(
                new File([metadataJson], fileName, { type: "application/json" })
              );
              if (!tokenUri.startsWith("ipfs://")) {
                throw new Error(
                  `Invalid IPFS URI for NFT #${Number(nextTokenId) + index}`
                );
              }
              return tokenUri;
            })
          );
          batchAmounts = batchMetadata.map((meta) => String(meta.amount));
          totalAmount = batchMetadata.reduce(
            (sum, meta) => sum + Number(meta.amount),
            0
          );
        } else {
          // Single mint cho SAME - Tạo tokenUri riêng cho mỗi NFT
          tokenUris = await Promise.all(
            Array(amount)
              .fill(null)
              .map(async (_, index) => {
                const metadataJson = JSON.stringify(
                  generateMetadata(
                    index,
                    baseName,
                    baseDescription,
                    baseImage,
                    baseAttributes
                  )
                );
                const fileName = `${baseName}-${address}-metadata-${uuidv4()}.json`;
                const tokenUri = await uploadFilePinata(
                  new File([metadataJson], fileName, {
                    type: "application/json",
                  })
                );
                if (!tokenUri.startsWith("ipfs://")) {
                  throw new Error(
                    `Invalid IPFS URI for NFT #${Number(nextTokenId) + index}`
                  );
                }
                return tokenUri;
              })
          );
        }

        const mintInput: MintNftInput = {
          mintPrice:
            mintCostData?.getMintCost.mintPrice || collection.mintPrice,
          collectionId: collection.id,
          chainId: collection.chainId,
          chain: collection.chain,
          contractAddress: collection.contractAddress,
          amount: totalAmount.toString(),
          tokenUris,
          owner: address,
          isBatch: isBatch,
          batchAmounts: isBatch ? batchAmounts : undefined,
          standard: collection.tokenStandard,
          stageId: isAllowlistMint
            ? activeStageData?.getActiveStage?.stageId
            : undefined,
          signature: isAllowlistMint ? signature : undefined,
          nonce: isAllowlistMint ? nonce : undefined,
          currency: collection.currency || chainObject?.nativeCurrency.symbol,
          metadata: isBatch
            ? batchMetadata.map((_, index) =>
                generateMetadata(
                  index,
                  baseName,
                  baseDescription,
                  baseImage,
                  baseAttributes
                )
              )
            : Array(amount)
                .fill(null)
                .map((_, index) =>
                  generateMetadata(
                    index,
                    baseName,
                    baseDescription,
                    baseImage,
                    baseAttributes
                  )
                ),
        };

        console.log(mintInput);
        const { data, errors } = await mintNft({
          variables: { input: mintInput },
          errorPolicy: "all",
        });

        if (errors || !data?.mintNft.success) {
          throw new Error(
            errors?.[0]?.message ||
              data?.mintNft.errorMessage ||
              "Failed to get mint steps"
          );
        }

        setMintSteps(data.mintNft.steps || []);
        setShowConfirmModal(true);
        return;
      } else {
        // Single mint cho UNIQUE
        tokenUris = await Promise.all(
          Array(amount)
            .fill(null)
            .map(async (_, index) => {
              const metadataJson = JSON.stringify(
                generateMetadata(
                  index,
                  name.trim() || collection.name,
                  description.trim() || collection.description || "",
                  imageFile!,
                  attributes.filter(
                    (attr) => attr.trait_type.trim() && attr.value.trim()
                  )
                )
              );
              const fileName = `${
                name || collection.name
              }-${address}-metadata-${uuidv4()}.json`;
              const tokenUri = await uploadFilePinata(
                new File([metadataJson], fileName, { type: "application/json" })
              );
              if (!tokenUri.startsWith("ipfs://")) {
                throw new Error(`Invalid IPFS URI for NFT #${index + 1}`);
              }
              return tokenUri;
            })
        );

        const mintInput: MintNftInput = {
          mintPrice:
            mintCostData?.getMintCost.mintPrice || collection.mintPrice,
          collectionId: collection.id,
          chainId: collection.chainId,
          chain: collection.chain,
          contractAddress: collection.contractAddress,
          amount: amount.toString(),
          owner: address,
          tokenUris,
          isBatch: false,
          standard: collection.tokenStandard,
          stageId: isAllowlistMint
            ? activeStageData?.getActiveStage?.stageId
            : undefined,
          signature: isAllowlistMint ? signature : undefined,
          nonce: isAllowlistMint ? nonce : undefined,
          metadata: [
            generateMetadata(
              0,
              name.trim() || collection.name,
              description.trim() || collection.description || "",
              imageFile!,
              attributes.filter(
                (attr) => attr.trait_type.trim() && attr.value.trim()
              )
            ),
          ],
        };

        console.log(mintInput);
        const { data, errors } = await mintNft({
          variables: { input: mintInput },
          errorPolicy: "all",
        });

        if (errors || !data?.mintNft.success) {
          throw new Error(
            errors?.[0]?.message ||
              data?.mintNft.errorMessage ||
              "Failed to get mint steps"
          );
        }

        setMintSteps(data.mintNft.steps || []);
        setShowConfirmModal(true);
      }
    } catch (error) {
      toast.error("Mint failed", { description: String(error) });
    }
  };

  const submitMint = async () => {
    try {
      if (!walletClient || !address || !collection) {
        throw new Error("Wallet not connected");
      }

      if (!mintSteps.length) {
        throw new Error("No mint steps available");
      }

      // Ký giao dịch qua MetaMask
      const step = JSON.parse(mintSteps[0].params);
      const tx = await walletClient.sendTransaction({
        to: step.to,
        value: BigInt(step.value),
        data: step.data,
        gas: BigInt(step.gasLimit),
      });

      // Gửi txHash về backend
      const submitInput: SubmitMintTransactionInput = {
        chainId: collection.chainId,
        contractAddress: collection.contractAddress,
        txHash: tx,
      };

      const { data, errors } = await submitMintTransaction({
        variables: { input: submitInput },
        errorPolicy: "all",
      });

      if (errors || !data?.submitMintTransaction.success) {
        throw new Error(
          errors?.[0]?.message ||
            data?.submitMintTransaction.errorMessage ||
            "Failed to submit transaction"
        );
      }

      toast.success("Mint successful!", {
        description: (
          <a
            href={`https://etherscan.io/tx/${data.submitMintTransaction.txHash}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            View on Etherscan
          </a>
        ),
      });

      onMintSuccess();
    } catch (error) {
      toast.error("Mint failed", { description: String(error) });
    } finally {
      setShowConfirmModal(false);
    }
  };

  if (!collection) {
    return <MintPanelSkeleton />;
  }

  return (
    <div className="bg-gray-50 dark:bg-[#0c0916] min-h-screen">
      <div className="max-w-6xl mx-auto p-4">
        <Tabs
          defaultValue="mint"
          className="w-full"
          onValueChange={setActiveTab}
          value={activeTab}
        >
          <TabsList
            className={`grid w-full max-w-md mx-auto mb-6 bg-white border border-gray-200 dark:bg-[#1a1625] dark:border-gray-800/50 ${
              isConnected ? "grid-cols-2" : "grid-cols-1"
            }`}
          >
            <TabsTrigger
              value="mint"
              className="data-[state=active]:bg-pink-500 dark:data-[state=active]:bg-pink-600 data-[state=active]:text-white text-sm"
            >
              Mint Your NFT
            </TabsTrigger>
            {isConnected && (
              <TabsTrigger
                value="history"
                className="data-[state=active]:bg-pink-500 dark:data-[state=active]:bg-pink-600 data-[state=active]:text-white text-sm"
              >
                History
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="mint" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Left Column - Mint Form */}
              <div className="bg-white border-gray-200 dark:bg-[#1a1625] rounded-lg overflow-hidden border dark:border-gray-800/50 shadow-sm">
                <div className="p-4 space-y-4">
                  {/* Collection Header */}
                  <div className="flex items-center gap-3">
                    <div className="relative w-12 h-12 rounded-md overflow-hidden border border-gray-200 dark:border-gray-800/50">
                      <Image
                        src={collection.image || "/placeholder.svg"}
                        alt={collection.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {collection.name}
                      </h3>
                      <div className="flex items-center gap-2 mt-1">
                        <span
                          className={`px-2 py-0.5 text-sm rounded-full ${
                            isSameArtType
                              ? "bg-blue-100 text-blue-600 border border-blue-200 dark:bg-blue-600/20 dark:text-blue-400 dark:border-blue-600/50"
                              : "bg-purple-100 text-purple-600 border border-purple-200 dark:bg-purple-600/20 dark:text-purple-400 dark:border-purple-600/50"
                          }`}
                        >
                          {isSameArtType ? "Same Art" : "Unique Art"}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Price Info */}
                  <div className="space-y-2 mt-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 dark:text-gray-400 text-sm">
                        Mint Price
                      </span>
                      <span className="text-gray-900 dark:text-white text-xl font-bold">
                        {mintCostData?.getMintCost.success
                          ? `${Number(
                              mintCostData.getMintCost.mintPrice
                            ).toFixed(4)} ETH`
                          : `${Number(lastMintCost.mintPrice).toFixed(4)} ETH`}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-1.5">
                        <span className="text-gray-600 dark:text-gray-400 text-xs">
                          Est. Gas
                        </span>
                        <Info className="h-3.5 w-3.5 text-gray-500" />
                      </div>
                      <span className="text-gray-600 dark:text-gray-400 text-xs">
                        {mintCostData?.getMintCost.success
                          ? `~${Number(
                              mintCostData.getMintCost.estimatedGas
                            ).toFixed(6)} ETH`
                          : `~${Number(lastMintCost.estimatedGas).toFixed(
                              6
                            )} ETH`}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 dark:text-gray-400 text-xs">
                        Stage
                      </span>
                      <span
                        className={`text-xs px-2 py-0.5 rounded-full ${
                          activeStageData?.getActiveStage?.isPublicMint
                            ? "bg-green-100 text-green-600 border border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800/50"
                            : "bg-red-100 text-red-600 border border-red-200 dark:bg-red-900/30 dark:text-red-400 dark:border-red-800/50"
                        }`}
                      >
                        {activeStageData?.getActiveStage?.isPublicMint
                          ? "Public"
                          : "Allowlist"}
                      </span>
                    </div>
                  </div>

                  {/* Mint Details */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                      Mint Details
                    </h4>
                    <MintStages />
                    <MintProgress />
                  </div>

                  {/* Mint Form */}
                  <MintInfo
                    isSameArtType={isSameArtType}
                    agreedToTerms={agreedToTerms}
                    setAgreedToTerms={setAgreedToTerms}
                    name={name}
                    setName={setName}
                    description={description}
                    setDescription={setDescription}
                    attributes={attributes}
                    setAttributes={setAttributes}
                    amount={amount}
                    setAmount={setAmount}
                    royalty={royalty}
                    setRoyalty={setRoyalty}
                    gasPrice={gasPrice}
                    setGasPrice={setGasPrice}
                    gasLimit={gasLimit}
                    setGasLimit={setGasLimit}
                    signature={signature}
                    setSignature={setSignature}
                    nonce={nonce}
                    setNonce={setNonce}
                    nameError={nameError}
                    descriptionError={descriptionError}
                    attributesError={attributesError}
                    imageFile={imageFile}
                    setImageFile={setImageFile}
                    handleFileUpload={handleFileUpload}
                    handleBatchUpload={handleBatchUpload}
                    isUploading={isUploading}
                    setIsUploading={setIsUploading}
                    batchMetadata={batchMetadata}
                    setBatchMetadata={setBatchMetadata}
                    isAllowlistMint={isAllowlistMint}
                  />

                  {/* Mint Button */}
                  <div className="md:sticky md:bottom-4">
                    <MintButton
                      agreedToTerms={agreedToTerms}
                      imageFile={imageFile}
                      name={name}
                      description={description}
                      attributes={attributes}
                      amount={amount}
                      royalty={royalty}
                      gasPrice={gasPrice}
                      gasLimit={gasLimit}
                      signature={signature}
                      nonce={nonce}
                      batchMetadata={batchMetadata}
                      onMintSuccess={onMintSuccess}
                      onMintError={(error) =>
                        toast.error("Mint failed", { description: error })
                      }
                      onConfirm={handleMintConfirm}
                      isSameArtType={isSameArtType}
                      isAllowlistMint={isAllowlistMint}
                      lastMintCost={lastMintCost}
                      isCalculating={isLoading}
                    />
                  </div>
                </div>
              </div>

              {/* Right Column - Preview */}
              <div className="space-y-4">
                <NftPreview
                  imageFile={imageFile}
                  name={name}
                  description={description}
                  attributes={attributes}
                  isSameArtType={isSameArtType}
                  collection={collection}
                  batchMetadata={batchMetadata}
                  currentGalleryImage={currentGalleryImage}
                />
                <div
                  className={`bg-white border-gray-200 dark:bg-[#1a1625] rounded-lg p-4 border dark:border-gray-800/50 shadow-sm ${
                    isSameArtType
                      ? "border-l-4 border-l-blue-500 dark:border-l-blue-600"
                      : "border-l-4 border-l-purple-500 dark:border-l-purple-600"
                  }`}
                >
                  <div className="flex items-center gap-2 mb-3">
                    <Sparkles
                      className={`h-5 w-5 ${
                        isSameArtType
                          ? "text-blue-500 dark:text-blue-400"
                          : "text-purple-500 dark:text-purple-400"
                      }`}
                    />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {isSameArtType
                        ? "Same Art Collection"
                        : "Unique Art Collection"}
                    </h3>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          {isConnected && (
            <TabsContent
              value="history"
              className="min-h-[800px] min-w-[700px]"
            >
              <div className="bg-white border-gray-200 dark:bg-[#1a1625] rounded-lg p-6 border dark:border-gray-800/50 shadow-sm min-h-[800px]">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Mint History
                </h2>

                <ScrollArea className="h-[700px]">
                  <div className="text-center py-12">
                    {nftsData?.getNfts.nfts.length === 0 ? (
                      <>
                        <p className="text-gray-600 dark:text-gray-400 text-sm">
                          No minted NFTs found
                        </p>
                        <Button
                          variant="outline"
                          className="mt-4 h-10 text-sm border-gray-300 dark:border-gray-800/50"
                          onClick={() => setActiveTab("mint")}
                        >
                          Mint Your First NFT
                        </Button>
                      </>
                    ) : (
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        {nftsData?.getNfts.nfts.map((nft) => (
                          <div
                            key={nft.id}
                            className="bg-gray-50 dark:bg-[#0f0a19] rounded-lg border border-gray-200 dark:border-gray-800/50 overflow-hidden flex flex-col"
                          >
                            <div className="relative aspect-square w-full bg-gray-100 dark:bg-gray-900/50">
                              {nft.image && (
                                <Image
                                  src={nft.image || "/placeholder.svg"}
                                  alt={nft.name || `NFT #${nft.tokenId}`}
                                  fill
                                  className="object-cover"
                                />
                              )}
                              <div className="absolute top-2 right-2">
                                <span
                                  className={`px-2 py-1 text-xs rounded-full font-medium ${
                                    nft.status === "COMPLETED"
                                      ? "bg-green-100 text-green-700 dark:bg-green-900/40 dark:text-green-400"
                                      : "bg-yellow-100 text-yellow-700 dark:bg-yellow-900/40 dark:text-yellow-400"
                                  }`}
                                >
                                  {nft.status}
                                </span>
                              </div>
                            </div>
                            <div className="p-3 flex-1">
                              <div className="flex justify-between items-start mb-1">
                                <h3 className="font-medium text-gray-900 dark:text-white text-sm truncate">
                                  {nft.name || `NFT #${nft.tokenId}`}
                                </h3>
                                <span className="text-xs text-gray-500 dark:text-gray-400 ml-1 whitespace-nowrap">
                                  #{nft.tokenId}
                                </span>
                              </div>
                              {nft.description && (
                                <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2 mb-2">
                                  {nft.description}
                                </p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </div>
            </TabsContent>
          )}
        </Tabs>
      </div>

      <Dialog open={showConfirmModal} onOpenChange={setShowConfirmModal}>
        <DialogContent
          className="bg-white border-gray-200 dark:bg-[#1a1625] dark:border-gray-800/50"
          onInteractOutside={(e) => {
            e.preventDefault();
          }}
        >
          <DialogHeader>
            <DialogTitle className="text-gray-900 dark:text-white">
              Confirm Mint
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-gray-700 dark:text-gray-300">
              You are about to mint{" "}
              <span className="font-medium">
                {currentAmount} NFT{currentAmount > 1 ? "s" : ""}
              </span>{" "}
              from <span className="font-medium">{collection.name}</span>.
            </p>
            <div className="bg-gray-50 dark:bg-[#0f0a19] p-4 rounded-md border border-gray-200 dark:border-gray-800/50">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                <span className="font-medium">Total Cost:</span>{" "}
                {mintCostData?.getMintCost.success
                  ? `${mintCostData.getMintCost.totalPrice} ETH`
                  : `${lastMintCost.totalPrice} ETH`}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                <span className="font-medium">Estimated Gas:</span>{" "}
                {mintCostData?.getMintCost.success
                  ? `${mintCostData.getMintCost.estimatedGas} ETH`
                  : `${lastMintCost.estimatedGas} ETH`}
              </p>
              {isBatchMint && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  <span className="font-medium">Batch Mint:</span>{" "}
                  {batchMetadata.length} unique NFTs
                </p>
              )}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Please ensure your wallet has sufficient funds. This action cannot
              be undone.
            </p>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowConfirmModal(false)}
              className="border-gray-300 text-gray-700 hover:bg-gray-100 dark:border-gray-800/50 dark:text-gray-300 dark:hover:bg-gray-800/50"
            >
              Cancel
            </Button>
            <Button
              onClick={submitMint}
              disabled={isMintingNft || isLoading}
              className="bg-pink-500 hover:bg-pink-600 text-white dark:bg-pink-600 dark:hover:bg-pink-700"
            >
              {isMintingNft || isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Sparkles className="mr-2 h-4 w-4" />
              )}
              {isMintingNft || isLoading ? "Calculating..." : "Confirm Mint"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
